import 'package:equatable/equatable.dart';

/// Configuration for image processing and upload
class ImageConfig extends Equatable {
  final int maxWidth;
  final int maxHeight;
  final int imageQuality;
  final int maxFileSizeBytes;
  final List<String> allowedExtensions;
  final bool enableTransformation;
  final int? transformWidth;
  final int? transformHeight;
  final int? transformQuality;

  const ImageConfig({
    required this.maxWidth,
    required this.maxHeight,
    required this.imageQuality,
    required this.maxFileSizeBytes,
    required this.allowedExtensions,
    this.enableTransformation = true,
    this.transformWidth,
    this.transformHeight,
    this.transformQuality,
  });

  /// Profile image configuration
  static const ImageConfig profile = ImageConfig(
    maxWidth: 512,
    maxHeight: 512,
    imageQuality: 80,
    maxFileSizeBytes: 5 * 1024 * 1024, // 5MB
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
    enableTransformation: true,
    transformWidth: 200,
    transformHeight: 200,
    transformQuality: 85,
  );

  /// Background image configuration
  static const ImageConfig background = ImageConfig(
    maxWidth: 1920,
    maxHeight: 1080,
    imageQuality: 85,
    maxFileSizeBytes: 10 * 1024 * 1024, // 10MB
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
    enableTransformation: true,
    transformWidth: 800,
    transformHeight: 450,
    transformQuality: 80,
  );

  /// Product image configuration
  static const ImageConfig product = ImageConfig(
    maxWidth: 1024,
    maxHeight: 1024,
    imageQuality: 85,
    maxFileSizeBytes: 8 * 1024 * 1024, // 8MB
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
    enableTransformation: true,
    transformWidth: 400,
    transformHeight: 400,
    transformQuality: 80,
  );

  @override
  List<Object?> get props => [
        maxWidth,
        maxHeight,
        imageQuality,
        maxFileSizeBytes,
        allowedExtensions,
        enableTransformation,
        transformWidth,
        transformHeight,
        transformQuality,
      ];
}
