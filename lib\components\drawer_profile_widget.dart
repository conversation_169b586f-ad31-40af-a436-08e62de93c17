import 'package:business_app/models/auth/user_model.dart';
import 'package:business_app/ui/user_profiles/profile_page.dart';
import 'package:flutter/material.dart';

/// A reusable widget for displaying user profile information in the drawer
class DrawerProfileWidget extends StatelessWidget {
  final UserModel? user;
  final bool isLoading;
  final VoidCallback? onTap;

  const DrawerProfileWidget({
    super.key,
    this.user,
    this.isLoading = false,
    this.onTap,
  });

  Widget _buildProfileImage(UserModel? user) {
    // Check if user has a profile image URL from database
    if (user?.profileImageUrl != null && user!.profileImageUrl!.isNotEmpty) {
      return Image.network(
        user.profileImageUrl!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
              strokeWidth: 2,
              color: const Color(0xFF1DA1F2),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // If network image fails, show grey avatar
          return _buildGreyAvatar();
        },
      );
    } else {
      // No profile image URL in database, show grey avatar
      return _buildGreyAvatar();
    }
  }

  Widget _buildGreyAvatar() {
    return Container(
      width: 60,
      height: 60,
      decoration: const BoxDecoration(
        color: Colors.grey,
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.person, color: Colors.white, size: 30),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 20.0),
        child: Center(
          child: CircularProgressIndicator(color: Color(0xFF1DA1F2)),
        ),
      );
    }

    return InkWell(
      onTap:
          onTap ??
          () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => MyProfilePage()),
            );
          },
      borderRadius: BorderRadius.circular(30),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            clipBehavior: Clip.antiAlias,
            decoration: const BoxDecoration(
              color: Colors.black26,
              shape: BoxShape.circle,
            ),
            child: _buildProfileImage(user),
          ),
          const SizedBox(width: 16.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user?.name ?? 'Loading...',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  '@${user?.username ?? 'loading'}',
                  style: const TextStyle(
                    fontSize: 14,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 8.0),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// A widget for displaying follower/following counts
class DrawerStatsWidget extends StatelessWidget {
  final UserModel? user;
  final bool isLoading;

  const DrawerStatsWidget({super.key, this.user, this.isLoading = false});

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const SizedBox.shrink();
    }

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          Text(
            _formatCount(user?.followersCount ?? 0),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const Text(' Customers'),
          const SizedBox(width: 16.0),
          Text(
            _formatCount(user?.followingCount ?? 0),
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const Text(' Following'),
        ],
      ),
    );
  }
}
