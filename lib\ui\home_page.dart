import 'package:business_app/ui/business_stats/business_stats.dart';
import 'package:business_app/components/custom_advanced_drawer.dart';
import 'package:business_app/components/custom_bottom_nav.dart';
import 'package:business_app/ui/posts/posts_ui.dart';
import 'package:business_app/ui/user_profiles/components/bottom_bar_pages/craete_post.dart';
import 'package:business_app/ui/user_profiles/components/bottom_bar_pages/messages_ui.dart';
import 'package:business_app/ui/user_profiles/components/bottom_bar_pages/search_ui.dart';
import 'package:business_app/ui/user_profiles/components/bottom_bar_pages/subscribe_ui.dart';
import 'package:business_app/const/assets.dart';
import 'package:business_app/const/constant_texts.dart';
import 'package:flutter_advanced_drawer/flutter_advanced_drawer.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

// Import BLoC
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_bloc.dart';
import 'package:business_app/bloc/posts_bloc/posts_event.dart';
import 'package:business_app/bloc/posts_bloc/posts_state.dart';
import 'package:business_app/bloc/navigation_bloc/navigation_bloc.dart';
import 'package:business_app/bloc/navigation_bloc/navigation_event.dart';
import 'package:business_app/bloc/navigation_bloc/navigation_state.dart';

// Import for new posts notification
import 'dart:async';

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _advancedDrawerController = AdvancedDrawerController();

  // New posts notification state
  bool _hasNewPosts = true; // Set to true initially for testing
  int _newPostsCount = 3; // Show 3 new posts initially
  Timer? _newPostsTimer;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Initialize BLoCs with initial data
    context.read<PostsBloc>().add(LoadPostsEvent());

    // Simulate new posts arriving (for demo purposes)
    _startNewPostsSimulation();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _newPostsTimer?.cancel();
    super.dispose();
  }

  void _handleMenuButtonPressed() {
    // Only toggle drawer when this specific method is called
    _advancedDrawerController.toggleDrawer();
  }

  // List of pages for bottom navigation -----------
  List<Widget> _buildPages(BuildContext context) => [
    // Home page with tabs
    NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is UserScrollNotification) {
          if (notification.direction == ScrollDirection.reverse) {
            context.read<NavigationBloc>().add(
              ToggleBottomBarVisibilityEvent(false),
            );
          } else if (notification.direction == ScrollDirection.forward) {
            context.read<NavigationBloc>().add(
              ToggleBottomBarVisibilityEvent(true),
            );
          }
        }
        return false;
      },
      child: NestedScrollView(
        physics: const BouncingScrollPhysics(),
        headerSliverBuilder:
            (context, innerBoxIsScrolled) => [
              SliverAppBar(
                floating: true,
                snap: true,
                pinned: false,
                backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                leading: IconButton(
                  onPressed: _handleMenuButtonPressed,
                  icon: ValueListenableBuilder<AdvancedDrawerValue>(
                    valueListenable: _advancedDrawerController,
                    builder:
                        (_, value, __) => AnimatedSwitcher(
                          duration: Duration(milliseconds: 300),
                          child:
                              value.visible
                                  ? Icon(
                                    Icons.clear,
                                    key: ValueKey<bool>(value.visible),
                                  )
                                  : ClipOval(
                                    child: Image.asset(
                                      Assets.trademateLogo,
                                      key: ValueKey<bool>(value.visible),
                                      width: 30.0,
                                      height: 30.0,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                        ),
                  ),
                ),
                title: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(child: Center(child: GoldText('Trademate'))),
                    _buildSpotifyPlanTag(),
                    /* Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Upgrade',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            //color: Color(0xFF1DA1F2),
                            color: Colors.amber,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(
                          //Icons.verified,
                          Icons.verified_outlined,
                          size: 20,
                          //color: Color(0xFF1DA1F2),
                          color: Colors.amber,
                        ),
                      ],
                    ),*/
                  ],
                ),
                bottom: TabBar(
                  controller: _tabController,
                  labelColor: Color(0xFF1DA1F2),
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: Color(0xFF1DA1F2),
                  indicatorWeight: 4,
                  labelStyle: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontWeight: FontWeight.normal,
                    fontSize: 16,
                  ),
                  tabs: [Tab(text: 'For You'), Tab(text: 'Following')],
                ),
              ),
            ],
        body: Stack(
          children: [
            TabBarView(
              controller: _tabController,
              children: [
                //MainPost(),
                _buildTabContent('For You'),
                _buildTabContent('Following'),
              ],
            ),
            // Twitter-style new posts notification
            _buildNewPostsNotification(),
          ],
        ),
      ),
    ),

    //bottom navigation bar pages-----------------------------------------------------------------------------------------------------------------------------------
    // Example: Search page
    /*SearchUi(
      onScrollBottomBarVisibility: (visible) {
        if (_isBottomBarVisible != visible) {
          setState(() => _isBottomBarVisible = visible);
        }
      },
    ),*/
    SearchUi(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(
          ToggleBottomBarVisibilityEvent(visible),
        );
      },
    ),
    //Example: Create post page
    /* CreatePostPage(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(ToggleBottomBarVisibilityEvent(visible));
      },
    ),*/
    //NewCreatePostPage(),
    CreatePost(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(
          ToggleBottomBarVisibilityEvent(visible),
        );
      },
    ),

    // Example: Profile page
    /* MessagesUi(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(ToggleBottomBarVisibilityEvent(visible));
      },
    ),*/
    MyMessagesPage(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(
          ToggleBottomBarVisibilityEvent(visible),
        );
      },
    ),
    //Example: premium page if unsubscribe
    /* SubscribeUi(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(ToggleBottomBarVisibilityEvent(visible));
      },
    ),*/
    SubscriptionPage(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(
          ToggleBottomBarVisibilityEvent(visible),
        );
      },
    ),

    /*
    NotificationsPage(
      onScrollBottomBarVisibility: (visible) {
        context.read<NavigationBloc>().add(
          ToggleBottomBarVisibilityEvent(visible),
        );
      },
    ),*/
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NavigationBloc, NavigationState>(
      builder: (context, navigationState) {
        final currentPage =
            _buildPages(context)[navigationState.selectedPageIndex];

        return CustomAdvancedDrawer(
          controller: _advancedDrawerController,
          child: _buildPageWithFAB(currentPage, navigationState),
        );
      },
    );
  }

  // Helper method to build page with appropriate FAB handling
  Widget _buildPageWithFAB(Widget page, NavigationState navigationState) {
    // Check if the page is a Scaffold and extract its FAB
    if (page is Scaffold && page.floatingActionButton != null) {
      // Page has its own FAB, use it directly
      return page;
    } else {
      // Page doesn't have FAB, wrap it in Scaffold with default FAB
      return Scaffold(
        extendBody: true,
        body: page,
        bottomNavigationBar: AnimatedSlide(
          duration: const Duration(milliseconds: 300),
          offset:
              navigationState.isBottomBarVisible ? Offset(0, 0) : Offset(0, 1),
          child: AnimatedOpacity(
            duration: const Duration(milliseconds: 300),
            opacity: navigationState.isBottomBarVisible ? 1.0 : 0.0,
            child: CustomBottomBar(
              currentIndex: navigationState.selectedPageIndex,
              onTap: (index) {
                context.read<NavigationBloc>().add(NavigateToPageEvent(index));
              },
            ),
          ),
        ),
        // Default FAB for pages that don't define their own
        floatingActionButton:
            _shouldShowDefaultFAB(navigationState.selectedPageIndex)
                ? HomeCustomExpandableFab()
                : null,
        floatingActionButtonLocation: ExpandableFab.location,
      );
    }
  }

  // Helper method to determine which pages should show the default FAB
  bool _shouldShowDefaultFAB(int pageIndex) {
    // Show default FAB for Home (0) page only
    // Other pages (Search, Create Post, Messages, Notifications) have their own custom FABs
    return pageIndex == 0; // Only show on Home page by default
  }

  // for tabs contents i.e post
  Widget _buildTabContent(String title) {
    return BlocBuilder<PostsBloc, PostsState>(
      builder: (context, state) {
        if (state.status == PostsStatus.loading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state.status == PostsStatus.error) {
          return Center(child: Text('Error: ${state.errorMessage}'));
        } else if (state.posts.isEmpty) {
          return const Center(child: Text('No posts available'));
        }

        return ListView.builder(
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.all(8),
          itemCount: state.posts.length,
          itemBuilder: (context, index) => MainPost(),
        );
      },
    );
  }

  // Simulate new posts arriving (for demo purposes)
  void _startNewPostsSimulation() {
    // Continue with periodic updates
    _newPostsTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        setState(() {
          _hasNewPosts = true;
          _newPostsCount = (_newPostsCount + 1) % 10 + 1; // Cycle between 1-10
        });
      }
    });
  }

  // Twitter-style new posts notification widget
  Widget _buildNewPostsNotification() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      top: _hasNewPosts ? 16 : -60,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: _onNewPostsButtonTapped,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF1DA1F2), // Twitter blue
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Overlapping profile images (Twitter style)
                _buildOverlappingProfileImages(),
                const SizedBox(width: 8),
                Text(
                  _newPostsCount == 1
                      ? 'See new post' // 'See new post'
                      : 'New posts', // 'See $_newPostsCount new posts',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 15,
                  ),
                ),
                const SizedBox(width: 6),
                const Icon(
                  Icons.keyboard_arrow_up,
                  color: Colors.white,
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Build overlapping profile images (Twitter style)
  Widget _buildOverlappingProfileImages() {
    // Mock profile image URLs - replace with real user data
    final List<String> profileImages = [
      'https://i.pravatar.cc/150?img=1',
      'https://i.pravatar.cc/150?img=2',
      'https://i.pravatar.cc/150?img=3',
      'https://i.pravatar.cc/150?img=4',
      'https://i.pravatar.cc/150?img=5',
    ];

    // Determine how many images to show (max 4)
    int imagesToShow = _newPostsCount > 4 ? 4 : _newPostsCount;

    return SizedBox(
      width: imagesToShow == 1 ? 20 : (20 + (imagesToShow - 1) * 12).toDouble(),
      height: 20,
      child: Stack(
        children: List.generate(imagesToShow, (index) {
          return Positioned(
            left: index * 12.0, // Overlap by 8px (20-12=8)
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1.5),
              ),
              child: ClipOval(
                child: Image.network(
                  profileImages[index % profileImages.length],
                  width: 17,
                  height: 17,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to colored circle with initials
                    return Container(
                      width: 17,
                      height: 17,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getProfileColor(index),
                      ),
                      child: Center(
                        child: Text(
                          _getProfileInitial(index),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  // Get profile color for fallback
  Color _getProfileColor(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.red,
    ];
    return colors[index % colors.length];
  }

  // Get profile initial for fallback
  String _getProfileInitial(int index) {
    final initials = ['A', 'B', 'C', 'D', 'E'];
    return initials[index % initials.length];
  }

  // Handle new posts button tap
  void _onNewPostsButtonTapped() {
    setState(() {
      _hasNewPosts = false;
      _newPostsCount = 0;
    });

    // Refresh posts and scroll to top
    context.read<PostsBloc>().add(LoadPostsEvent());

    // Show a brief feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'Refreshed with new posts!',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF1DA1F2),
        duration: const Duration(milliseconds: 800),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
}

// floating action button

class HomeCustomExpandableFab extends StatelessWidget {
  const HomeCustomExpandableFab({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ExpandableFabState> fabKey =
        GlobalKey<ExpandableFabState>();

    return ExpandableFab(
      key: fabKey,
      type: ExpandableFabType.up,
      distance: 60,
      childrenAnimation: ExpandableFabAnimation.none,
      // fanAngle: 40,
      openButtonBuilder: RotateFloatingActionButtonBuilder(
        child: const Icon(Icons.add, size: 40),
        fabSize: ExpandableFabSize.regular,
        foregroundColor: Colors.amber,
        backgroundColor: Colors.blue,
        shape: const CircleBorder(),
        angle: 3.14 * 2,
        elevation: 2,
      ),
      closeButtonBuilder: FloatingActionButtonBuilder(
        size: 24,
        builder: (
          BuildContext context,
          void Function()? onPressed,
          Animation<double> progress,
        ) {
          return IconButton(
            onPressed: onPressed,
            icon: const Icon(
              Icons.check_circle_outline,
              size: 40,
              color: Colors.amber,
            ),
          );
        },
      ),
      overlayStyle: ExpandableFabOverlayStyle(
        color:
            Theme.of(context).brightness == Brightness.dark
                ? Colors.black.withOpacity(0.9)
                : Colors.white.withOpacity(0.9),
      ),

      children: [
        _buildActionRow(
          label: 'Go Live',
          icon: Icons.video_call_outlined,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Categories',
          icon: Icons.sort,
          onPressed: () {},
        ),
        _buildActionRow(
          label: 'Business Stats',
          icon: Icons.bar_chart,
          onPressed: () {
            Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (context) => BusinessStats()));
          },
        ),

        /*FloatingActionButton.small(
          heroTag: null,
          tooltip: 'Add',
          onPressed: () => _handleAction('Add'),
          child: const Icon(Icons.add),
        ),*/
      ],
    );
  }

  Widget _buildActionRow({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          margin: const EdgeInsets.only(right: 10),
          decoration: BoxDecoration(
            //color: Colors.deepPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(32),
          ),
          child: Text(label, style: const TextStyle(fontSize: 16)),
        ),
        FloatingActionButton.small(
          heroTag: null,
          tooltip: label,
          onPressed: onPressed,
          backgroundColor: Colors.white,
          foregroundColor: Colors.blue, //Colors.deepPurple,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(32),
          ),
          // elevation: 2,
          highlightElevation: 4,
          focusElevation: 4,
          hoverElevation: 4,

          // splashColor: Colors.deepPurple.withOpacity(0.2),
          // highlightColor: Colors.deepPurple.withOpacity(0.2),
          child: Icon(icon),
        ),
      ],
    );
  }
}

// FAB code ends here

// subscription widget in the app bar
Widget _buildSpotifyPlanTag() {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
    decoration: BoxDecoration(
      color: Colors.white70,
      borderRadius: BorderRadius.circular(5),
      border: Border.all(color: Colors.white30),
      boxShadow: [
        BoxShadow(
          color: Colors.white.withOpacity(0.05),
          blurRadius: 6,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: const Text(
      "Free",
      style: TextStyle(
        color: Colors.black,
        fontWeight: FontWeight.w500,
        fontSize: 12,
        letterSpacing: 0.4,
      ),
    ),
  );
}
