name: business_app
description: "trading & marketplace center."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 8.0.21+100

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.5.3
  provider: ^6.1.4
  google_fonts: ^6.2.1
  rive_animated_icon: ^2.0.5
  flutter_advanced_drawer: ^1.4.0
  font_awesome_flutter: ^10.8.0
  google_nav_bar: ^5.0.7
  scroll_bottom_navigation_bar: ^4.0.0
  floating_frosted_bottom_bar: ^0.0.1
  cached_network_image: ^3.4.1
  floating_bottom_navigation_bar: ^1.5.2
  flutter_animate: ^4.5.2
  wolt_modal_sheet: ^0.11.0
  flutter_expandable_fab: ^2.5.1
  emoji_picker_flutter: ^4.3.0
  flutter_blur: ^0.0.2
  smooth_page_indicator: ^1.2.1
  flutter_screenutil: ^5.9.3
  fl_chart: ^1.0.0
  syncfusion_flutter_charts: ^30.1.37
  flutter_launcher_icons: ^0.14.4
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1
  flutter_localization: ^0.3.3
  package_info_plus: ^8.3.0
  url_launcher: ^6.3.1
  permission_handler: ^12.0.1
  path_provider: ^2.1.5
  fluttertoast: ^8.2.12
  lucide_icons: ^0.257.0
  equatable: ^2.0.5
  http: ^1.1.0
  image_picker: ^1.0.4
  supabase_flutter: ^2.3.4
  phone_numbers_parser: ^9.0.3
  connectivity_plus: ^6.1.4
  internet_connection_checker_plus: ^2.5.2
  app_settings: ^6.1.1
  google_sign_in: ^7.1.1
  google_sign_in_ios: ^6.1.0
  logger: ^2.6.1
  path: ^1.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_launcher_icons:
  android: true #"launcher_icon"
  ios: true
  image_path: "assets/icon/logo.png"
  adaptive_icon_background: "assets/icon/solid-color-logo.png" #"#FFFFFF"
  adaptive_icon_foreground: "assets/icon/transparent-logo.png"
  adaptive_icon_monochrome: "assets/icon/adaptive-logo.png"
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/wall.jpg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SF-Pro-Display
      fonts:
        - asset: fonts/SFPRODISPLAYBLACKITALIC.OTF 
        - asset: fonts/SFPRODISPLAYBOLD.OTF 
        - asset: fonts/SFPRODISPLAYHEAVYITALIC.OTF 
        - asset: fonts/SFPRODISPLAYLIGHTITALIC.OTF 
        - asset: fonts/SFPRODISPLAYMEDIUM.OTF 
        - asset: fonts/SFPRODISPLAYREGULAR.OTF 
        - asset: fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF 
        - asset: fonts/SFPRODISPLAYTHINITALIC.OTF 
        - asset: fonts/SFPRODISPLAYULTRALIGHTITALIC.OTF

  # fonts:
  #   - family: Sofia-Pro-font
  #     fonts:
  #       - asset: Sofia-Pro-Font/Sofia Pro Black Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Black Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Bold Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Bold Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro ExtraLight Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro ExtraLight Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Light Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Light Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Medium Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Medium Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Regular Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Regular Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Semi Bold Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro Semi Bold Italic Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro UltraLight Az.otf 
  #       - asset: Sofia-Pro-Font/Sofia Pro UltraLight Italic Az.otf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
