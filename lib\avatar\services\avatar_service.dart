import 'dart:io';
import 'package:logger/logger.dart';
import '../../models/auth/auth_result.dart';
import '../models/image_config.dart';
import 'supabase_storage_service.dart';

/// Comprehensive avatar service for managing profile and background images
class AvatarService {
  static final Logger _logger = Logger();
  static const String _logTag = '[AvatarService]';

  /// Upload profile image with comprehensive error handling
  static Future<AuthResult<String>> uploadProfileImage({
    required String userId,
    required File imageFile,
    bool replaceExisting = true,
  }) async {
    try {
      _logger.i('$_logTag 📤 Starting profile image upload for user: $userId');

      final result = await SupabaseStorageService.uploadProfileImage(
        userId: userId,
        imageFile: imageFile,
        replaceExisting: replaceExisting,
      );

      if (result.isSuccess && result.imageUrl != null) {
        _logger.i('$_logTag ✅ Profile image uploaded successfully');
        return AuthResult.success(result.imageUrl!);
      } else {
        _logger.e('$_logTag ❌ Profile image upload failed: ${result.error}');
        return AuthResult.error(result.error ?? 'Unknown upload error');
      }
    } catch (e) {
      final error = 'Profile image upload failed: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Upload background image with comprehensive error handling
  static Future<AuthResult<String>> uploadBackgroundImage({
    required String userId,
    required File imageFile,
    bool replaceExisting = true,
  }) async {
    try {
      _logger.i(
        '$_logTag 📤 Starting background image upload for user: $userId',
      );

      final result = await SupabaseStorageService.uploadBackgroundImage(
        userId: userId,
        imageFile: imageFile,
        replaceExisting: replaceExisting,
      );

      if (result.isSuccess && result.imageUrl != null) {
        _logger.i('$_logTag ✅ Background image uploaded successfully');
        return AuthResult.success(result.imageUrl!);
      } else {
        _logger.e('$_logTag ❌ Background image upload failed: ${result.error}');
        return AuthResult.error(result.error ?? 'Unknown upload error');
      }
    } catch (e) {
      final error = 'Background image upload failed: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Delete profile image
  static Future<AuthResult<bool>> deleteProfileImage(String imageUrl) async {
    try {
      _logger.i('$_logTag 🗑️ Deleting profile image: $imageUrl');

      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        imageUrl,
        SupabaseStorageService.profileImagesBucket,
      );

      if (fileName == null) {
        const error = 'Invalid profile image URL';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      final result = await SupabaseStorageService.deleteImage(
        bucket: SupabaseStorageService.profileImagesBucket,
        fileName: fileName,
      );

      if (result.isSuccess) {
        _logger.i('$_logTag ✅ Profile image deleted successfully');
      } else {
        _logger.e('$_logTag ❌ Profile image deletion failed: ${result.error}');
      }

      return result;
    } catch (e) {
      final error = 'Profile image deletion failed: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Delete background image
  static Future<AuthResult<bool>> deleteBackgroundImage(String imageUrl) async {
    try {
      _logger.i('$_logTag 🗑️ Deleting background image: $imageUrl');

      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        imageUrl,
        SupabaseStorageService.backgroundImagesBucket,
      );

      if (fileName == null) {
        const error = 'Invalid background image URL';
        _logger.e('$_logTag ❌ $error');
        return AuthResult.error(error);
      }

      final result = await SupabaseStorageService.deleteImage(
        bucket: SupabaseStorageService.backgroundImagesBucket,
        fileName: fileName,
      );

      if (result.isSuccess) {
        _logger.i('$_logTag ✅ Background image deleted successfully');
      } else {
        _logger.e(
          '$_logTag ❌ Background image deletion failed: ${result.error}',
        );
      }

      return result;
    } catch (e) {
      final error = 'Background image deletion failed: ${e.toString()}';
      _logger.e('$_logTag ❌ $error');
      return AuthResult.error(error);
    }
  }

  /// Get optimized profile image URL
  static String? getOptimizedProfileImageUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) return null;

    try {
      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        imageUrl,
        SupabaseStorageService.profileImagesBucket,
      );

      if (fileName != null) {
        return SupabaseStorageService.getOptimizedProfileImageUrl(fileName);
      }

      return imageUrl; // Return original URL as fallback
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to get optimized profile image URL: $e');
      return imageUrl; // Return original URL as fallback
    }
  }

  /// Get optimized background image URL
  static String? getOptimizedBackgroundImageUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) return null;

    try {
      final fileName = SupabaseStorageService.extractFileNameFromUrl(
        imageUrl,
        SupabaseStorageService.backgroundImagesBucket,
      );

      if (fileName != null) {
        return SupabaseStorageService.getOptimizedBackgroundImageUrl(fileName);
      }

      return imageUrl; // Return original URL as fallback
    } catch (e) {
      _logger.e('$_logTag ❌ Failed to get optimized background image URL: $e');
      return imageUrl; // Return original URL as fallback
    }
  }

  /// Validate image file before upload
  static Future<String?> validateImageFile(
    File imageFile,
    ImageConfig config,
  ) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        return 'Image file does not exist';
      }

      // Check file size
      final fileSize = await imageFile.length();
      if (fileSize > config.maxFileSizeBytes) {
        final maxSizeMB = (config.maxFileSizeBytes / (1024 * 1024))
            .toStringAsFixed(1);
        return 'Image must be less than ${maxSizeMB}MB';
      }

      // Check file extension
      final extension = imageFile.path.split('.').last.toLowerCase();
      final allowedExtensions =
          config.allowedExtensions.map((e) => e.replaceAll('.', '')).toList();
      if (!allowedExtensions.contains(extension)) {
        return 'Invalid format. Allowed: ${allowedExtensions.join(', ')}';
      }

      return null; // Valid image
    } catch (e) {
      _logger.e('$_logTag ❌ Image validation error: $e');
      return 'Failed to validate image';
    }
  }

  /// Get image configurations
  static ImageConfig getProfileImageConfig() => ImageConfig.profile;
  static ImageConfig getBackgroundImageConfig() => ImageConfig.background;
  static ImageConfig getProductImageConfig() => ImageConfig.product;

  /// Get storage bucket names
  static String get profileImagesBucket =>
      SupabaseStorageService.profileImagesBucket;
  static String get backgroundImagesBucket =>
      SupabaseStorageService.backgroundImagesBucket;
  static String get productImagesBucket =>
      SupabaseStorageService.productImagesBucket;
}
